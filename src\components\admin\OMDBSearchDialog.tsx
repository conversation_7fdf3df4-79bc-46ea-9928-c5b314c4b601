import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Search, Loader2, Calendar, Star, Play, Film, Database } from "lucide-react";
import { 
  searchOMDbContent, 
  formatOMDbData, 
  convertOMDbType,
  hasValidPoster,
  OMDbMovie 
} from "@/services/omdbService";

interface OMDBSearchDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectContent: (imdbId: string, contentType: 'movie' | 'tv', data?: any) => void;
}

export default function OMDBSearchDialog({ isOpen, onClose, onSelectContent }: OMDBSearchDialogProps) {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<OMDbMovie[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchType, setSearchType] = useState<'movie' | 'series' | 'all'>('all');

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Error",
        description: "Please enter a search query",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);

    try {
      const results = await searchOMDbContent(
        searchQuery,
        searchType === 'all' ? undefined : searchType
      );

      setSearchResults(results.Search || []);

      toast({
        title: "Search Complete",
        description: `Found ${results.totalResults || 0} results`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Search failed";
      
      toast({
        title: "Search Failed",
        description: errorMessage,
        variant: "destructive",
      });

      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectContent = (movie: OMDbMovie) => {
    const contentType = convertOMDbType(movie.Type);
    const formattedData = formatOMDbData(movie);
    
    onSelectContent(movie.imdbID, contentType, formattedData);
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col bg-[#0a0a0a] border border-[#e6cb8e]/20">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-[#e6cb8e] flex items-center gap-2">
            <Database className="w-5 h-5" />
            Search OMDb Database
          </DialogTitle>
        </DialogHeader>

        <div className="flex-shrink-0 space-y-4">
          {/* Search Controls */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Input
                placeholder="Search for movies or TV series..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="bg-[#1a1a1a] border-[#e6cb8e]/30 text-white placeholder-gray-400 focus:border-[#e6cb8e]"
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching}
              className="bg-[#e6cb8e] text-black hover:bg-[#e6cb8e]/90 px-6"
            >
              {isSearching ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Search className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Search Type Filter */}
          <div className="flex gap-2">
            <Button
              variant={searchType === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchType('all')}
              className={searchType === 'all' 
                ? 'bg-[#e6cb8e] text-black hover:bg-[#e6cb8e]/90' 
                : 'border-[#e6cb8e]/30 text-[#e6cb8e] hover:bg-[#e6cb8e]/10'
              }
            >
              All
            </Button>
            <Button
              variant={searchType === 'movie' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchType('movie')}
              className={searchType === 'movie' 
                ? 'bg-[#e6cb8e] text-black hover:bg-[#e6cb8e]/90' 
                : 'border-[#e6cb8e]/30 text-[#e6cb8e] hover:bg-[#e6cb8e]/10'
              }
            >
              <Film className="w-4 h-4 mr-1" />
              Movies
            </Button>
            <Button
              variant={searchType === 'series' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchType('series')}
              className={searchType === 'series' 
                ? 'bg-[#e6cb8e] text-black hover:bg-[#e6cb8e]/90' 
                : 'border-[#e6cb8e]/30 text-[#e6cb8e] hover:bg-[#e6cb8e]/10'
              }
            >
              <Play className="w-4 h-4 mr-1" />
              TV Series
            </Button>
          </div>
        </div>

        {/* Search Results */}
        <div className="flex-1 overflow-y-auto">
          {searchResults.length > 0 ? (
            <div className="grid gap-4 mt-4">
              {searchResults.map((movie) => (
                <div
                  key={movie.imdbID}
                  className="flex gap-4 p-4 bg-[#1a1a1a] rounded-lg border border-[#e6cb8e]/10 hover:border-[#e6cb8e]/30 transition-colors cursor-pointer"
                  onClick={() => handleSelectContent(movie)}
                >
                  {/* Poster */}
                  <div className="flex-shrink-0 w-16 h-24 bg-[#2a2a2a] rounded overflow-hidden">
                    {hasValidPoster(movie.Poster) ? (
                      <img
                        src={movie.Poster}
                        alt={movie.Title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Film className="w-6 h-6 text-gray-500" />
                      </div>
                    )}
                  </div>

                  {/* Content Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-medium text-sm truncate">
                          {movie.Title}
                        </h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge 
                            variant="secondary" 
                            className="bg-[#e6cb8e]/20 text-[#e6cb8e] text-xs"
                          >
                            {movie.Type === 'series' ? 'TV Series' : 'Movie'}
                          </Badge>
                          {movie.Year && (
                            <div className="flex items-center gap-1 text-gray-400 text-xs">
                              <Calendar className="w-3 h-3" />
                              {movie.Year}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Additional Info */}
                    <div className="mt-2 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <Database className="w-3 h-3" />
                        IMDb ID: {movie.imdbID}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-32 text-gray-400">
              {isSearching ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Searching OMDb...
                </div>
              ) : (
                <div className="text-center">
                  <Database className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Search for movies and TV series in the OMDb database</p>
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
